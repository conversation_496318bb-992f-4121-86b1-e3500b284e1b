/**
 * @vitest-environment jsdom
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  getCookie,
  setCookie,
  deleteCookie,
  clearAuthCookies,
  hasAuthCookies,
  getAllCookies,
  clearCookiesByPattern,
} from '../cookies';

// Mock document.cookie
Object.defineProperty(document, 'cookie', {
  writable: true,
  value: '',
});

describe('Cookie utilities', () => {
  beforeEach(() => {
    // Clear all cookies before each test
    document.cookie = '';
  });

  describe('getCookie', () => {
    it('should return null when cookie does not exist', () => {
      expect(getCookie('nonexistent')).toBeNull();
    });

    it('should return cookie value when cookie exists', () => {
      document.cookie = 'testCookie=testValue';
      expect(getCookie('testCookie')).toBe('testValue');
    });

    it('should handle multiple cookies', () => {
      document.cookie = 'cookie1=value1; cookie2=value2';
      expect(getCookie('cookie1')).toBe('value1');
      expect(getCookie('cookie2')).toBe('value2');
    });
  });

  describe('setCookie', () => {
    it('should set a simple cookie', () => {
      setCookie('testCookie', 'testValue');
      expect(document.cookie).toContain('testCookie=testValue');
    });

    it('should set cookie with path', () => {
      setCookie('testCookie', 'testValue', { path: '/test' });
      expect(document.cookie).toContain('testCookie=testValue');
      expect(document.cookie).toContain('path=/test');
    });

    it('should set cookie with domain', () => {
      setCookie('testCookie', 'testValue', { domain: 'example.com' });
      expect(document.cookie).toContain('testCookie=testValue');
      expect(document.cookie).toContain('domain=example.com');
    });

    it('should set secure cookie', () => {
      setCookie('testCookie', 'testValue', { secure: true });
      expect(document.cookie).toContain('testCookie=testValue');
      expect(document.cookie).toContain('secure');
    });
  });

  describe('deleteCookie', () => {
    it('should delete a cookie', () => {
      // Set a cookie first
      document.cookie = 'testCookie=testValue';
      expect(getCookie('testCookie')).toBe('testValue');

      // Delete it
      deleteCookie('testCookie');
      
      // Check that it's been set to expire in the past
      expect(document.cookie).toContain('testCookie=');
      expect(document.cookie).toContain('expires=Thu, 01 Jan 1970 00:00:00 GMT');
    });
  });

  describe('getAllCookies', () => {
    it('should return empty object when no cookies exist', () => {
      expect(getAllCookies()).toEqual({});
    });

    it('should return all cookies as object', () => {
      document.cookie = 'cookie1=value1; cookie2=value2';
      const cookies = getAllCookies();
      expect(cookies).toEqual({
        cookie1: 'value1',
        cookie2: 'value2',
      });
    });
  });

  describe('clearCookiesByPattern', () => {
    it('should clear cookies matching string pattern', () => {
      document.cookie = '__clerk_jwt=value1; __clerk_session=value2; other_cookie=value3';
      
      clearCookiesByPattern('__clerk');
      
      // Should have cleared __clerk cookies but not others
      expect(document.cookie).toContain('other_cookie=value3');
      expect(document.cookie).toContain('__clerk_jwt=');
      expect(document.cookie).toContain('__clerk_session=');
    });

    it('should clear cookies matching regex pattern', () => {
      document.cookie = '__clerk_jwt=value1; __session_id=value2; other_cookie=value3';
      
      clearCookiesByPattern(/^(__clerk|__session)/);
      
      // Should have cleared matching cookies but not others
      expect(document.cookie).toContain('other_cookie=value3');
      expect(document.cookie).toContain('__clerk_jwt=');
      expect(document.cookie).toContain('__session_id=');
    });
  });

  describe('hasAuthCookies', () => {
    it('should return false when no auth cookies exist', () => {
      document.cookie = 'regular_cookie=value';
      expect(hasAuthCookies()).toBe(false);
    });

    it('should return true when clerk cookies exist', () => {
      document.cookie = '__clerk_db_jwt=value';
      expect(hasAuthCookies()).toBe(true);
    });

    it('should return true when session cookies exist', () => {
      document.cookie = '__session=value';
      expect(hasAuthCookies()).toBe(true);
    });

    it('should return true when auth_token exists', () => {
      document.cookie = 'auth_token=value';
      expect(hasAuthCookies()).toBe(true);
    });
  });

  describe('clearAuthCookies', () => {
    it('should clear all authentication cookies', () => {
      // Set various auth cookies
      document.cookie = '__clerk_db_jwt=value1; __session=value2; auth_token=value3; regular_cookie=value4';
      
      expect(hasAuthCookies()).toBe(true);
      
      clearAuthCookies();
      
      expect(hasAuthCookies()).toBe(false);
      expect(document.cookie).toContain('regular_cookie=value4'); // Should keep non-auth cookies
    });

    it('should handle case when no auth cookies exist', () => {
      document.cookie = 'regular_cookie=value';
      
      expect(() => clearAuthCookies()).not.toThrow();
      expect(hasAuthCookies()).toBe(false);
      expect(document.cookie).toContain('regular_cookie=value');
    });
  });
});
