# Utility Functions

## Cookie Management (`cookies.ts`)

Utilities for managing browser cookies, especially useful for authentication and session management.

### Functions

#### `getCookie(name: string): string | null`

Get a cookie value by name.

```typescript
const token = getCookie('auth_token');
```

#### `setCookie(name: string, value: string, options?: CookieOptions): void`

Set a cookie with optional configuration.

```typescript
setCookie('auth_token', 'abc123', {
  expires: new Date(Date.now() + 86400000), // 1 day
  path: '/',
  secure: true,
  sameSite: 'strict',
});
```

#### `deleteCookie(name: string, options?: DeleteCookieOptions): void`

Delete a cookie by setting it to expire in the past.

```typescript
deleteCookie('auth_token');
```

#### `clearAuthCookies(): void`

Clear all authentication-related cookies (Clerk, session, auth tokens).

```typescript
clearAuthCookies(); // Clears __clerk_*, __session*, auth_token, etc.
```

#### `hasAuthCookies(): boolean`

Check if any authentication cookies exist.

```typescript
if (hasAuthCookies()) {
  console.log('User has active session cookies');
}
```

#### `clearCookiesByPattern(pattern: RegExp | string): void`

Clear all cookies matching a pattern.

```typescript
clearCookiesByPattern(/^__clerk/); // Clear all cookies starting with __clerk
clearCookiesByPattern('session'); // Clear all cookies containing 'session'
```

## User Data Clearing (`clearUserData.ts`)

### `clearAllUserData(): Promise<void>`

Comprehensive function to clear all user data when signing out:

1. **SWR cache** - Clears all cached API responses
2. **Zustand stores** - Resets all application state
3. **localStorage** - Removes user-related data
4. **sessionStorage** - Clears all session data
5. **Cookies** - Removes authentication cookies (NEW!)
6. **Guest user creation** - Creates new guest session

### Usage

```typescript
import { clearAllUserData } from '@/utils/clearUserData';

// In sign-out handler
const handleSignOut = async () => {
  try {
    await clearAllUserData();
    router.push('/');
  } catch (error) {
    console.error('Failed to clear user data:', error);
  }
};
```

## Testing (`testClearUserData.ts`)

### Functions for testing the clear functionality:

#### `addTestData(): void`

Adds test data to stores, localStorage, and cookies.

#### `checkDataCleared(): { results: object, allCleared: boolean }`

Checks if all data was properly cleared, including cookies.

#### `testClearUserData(): Promise<boolean>`

Runs a complete test cycle: add data → clear data → verify clearing.

### Usage

```typescript
// In browser console or test environment
import { testClearUserData } from '@/utils/testClearUserData';

const success = await testClearUserData();
console.log('Clear test passed:', success);
```

## Cookie Clearing Fix

The main issue was that cookies (especially Clerk authentication cookies) were not being cleared during logout, requiring a page reload to fully sign out.

### What was fixed:

1. **Added comprehensive cookie clearing** - Now clears all authentication cookies immediately
2. **Handles multiple cookie scenarios** - Clears cookies with different paths, domains, and security flags
3. **Pattern-based clearing** - Automatically finds and clears cookies matching authentication patterns
4. **Immediate effect** - No page reload required for complete logout

### Cookies that are now cleared:

- `__clerk_db_jwt` - Clerk JWT token
- `__session` - Session cookies
- `__clerk_*` - All Clerk-related cookies
- `auth_token` - Custom auth tokens
- `session_token` - Session tokens
- `user_session` - User session data

The fix ensures that when users sign out, all authentication state is immediately cleared from both application state and browser cookies, providing a complete and secure logout experience.

## Updated Cookie Clearing Solution

### Problem

Cookies (especially Clerk authentication cookies) were not being cleared during logout, requiring a page reload to fully sign out. This happened because:

1. **HttpOnly cookies** cannot be cleared via JavaScript
2. **Cross-domain cookies** require specific domain/path combinations
3. **Dynamic cookie names** (like `__clerk_db_jwt_FLMvHKIX`) were not being detected
4. **Multiple security flags** (Secure, SameSite) require different clearing approaches

### Solution

#### 1. Enhanced Cookie Utilities (`cookies.ts`)

**New Functions:**

- `aggressiveClearAuthCookies()` - Tries multiple approaches to clear cookies
- `clearCookiesByPattern()` - Clear cookies matching regex patterns
- `hasAuthCookies()` - Check if any auth cookies remain
- `getAllCookies()` - Get all accessible cookies

#### 2. Server-Side Cookie Clearing (`/api/auth/clear-cookies`)

API endpoint that sets `Set-Cookie` headers to clear HttpOnly cookies that JavaScript cannot access.

#### 3. Complete Logout Integration (`clearUserData.ts`)

**New Functions:**

- `completeLogout()` - Integrates Clerk signOut with comprehensive data clearing
- Enhanced `clearAllUserData()` - Now includes aggressive cookie clearing

#### 4. React Hooks (`useCompleteLogout.ts`)

**Hooks for easy integration:**

- `useCompleteLogout()` - Complete logout with optional redirect
- `useEmergencyLogout()` - Force logout with page reload

### Usage Examples

#### Basic Usage (Recommended)

```typescript
import { useCompleteLogout } from '@/hooks/useCompleteLogout';

const { logout } = useCompleteLogout();

// Complete logout with redirect
await logout({ redirectTo: '/' });

// Complete logout with forced reload (for stubborn cookies)
await logout({ redirectTo: '/', forceReload: true });
```

#### Emergency Logout (When normal logout fails)

```typescript
import { useEmergencyLogout } from '@/hooks/useCompleteLogout';

const { emergencyLogout } = useEmergencyLogout();

// Force logout with immediate page reload
await emergencyLogout();
```

#### Testing Cookie Clearing

```typescript
// In browser console
await testAggressiveCookieClearing();
```

### What Gets Cleared Now

1. **All Clerk cookies** including dynamic ones:
   - `__clerk_db_jwt_*`
   - `__client_uat_*`
   - `__refresh_*`
   - `__session_*`

2. **Cross-domain cookies** from:
   - `localhost`
   - `.heyalfie.com`
   - `.clerk.com`
   - `.summary-boa-78.clerk.accounts.dev`

3. **All security flag combinations**:
   - Regular cookies
   - Secure cookies
   - HttpOnly cookies (via server endpoint)
   - SameSite variations (Lax, Strict, None)

### Fallback Strategy

If cookies still remain after all clearing attempts:

1. Log warning message
2. Optionally force page reload
3. Provide emergency logout hook as backup

This multi-layered approach ensures maximum compatibility across different browser configurations and cookie settings.
