/**
 * Test utility to verify that clearAllUserData works correctly
 */

import { useAuthStore } from '@/stores/auth.store';
import { useRedirectQueryUrl } from '@/hooks/useRedirectQueryUrl';
import { useOnboardingStore } from '@/hooks/useOnboarding';
import { useChats } from '@/hooks/useChats';
import { useWidgets } from '@/hooks/useWidgets';
import { clearAllUserData } from './clearUserData';
import { UserPropertyRelationType } from '@/api/properties';
import { setCookie, hasAuthCookies } from './cookies';

/**
 * Add some test data to stores and localStorage
 */
export const addTestData = () => {
  console.log('🧪 Adding test data...');

  // Add test data to stores
  useAuthStore.getState().setGuestAuth({
    token: 'test-token-123',
    userId: 123,
    expiresAt: new Date(Date.now() + 3600000).toISOString(),
  });

  useRedirectQueryUrl.getState().setRedirectUrl('https://example.com/test');

  useOnboardingStore.getState().setSelectedRole(UserPropertyRelationType.OwnerAndOccupier);
  useOnboardingStore.getState().setSelectedAddress('Test Address 123');

  useChats.setState({
    chats: [
      {
        id: 1,
        title: 'Test Chat',
        createdAt: new Date().toISOString(),
        status: '',
      },
    ],
  });

  // Add test localStorage data
  localStorage.setItem('user-test-data', 'test-value');
  localStorage.setItem('chat-history', 'test-chat-data');

  // Add test cookies
  setCookie('__clerk_db_jwt', 'test-jwt-token', { path: '/' });
  setCookie('__session', 'test-session-token', { path: '/' });
  setCookie('auth_token', 'test-auth-token', { path: '/' });

  console.log('✅ Test data added');
};

/**
 * Check if data was cleared properly
 */
export const checkDataCleared = () => {
  console.log('🔍 Checking if data was cleared...');

  const authState = useAuthStore.getState();
  const redirectState = useRedirectQueryUrl.getState();
  const onboardingState = useOnboardingStore.getState();
  const chatsState = useChats.getState();
  const widgetsState = useWidgets.getState();

  const results = {
    authCleared: !authState.guestAuth,
    redirectCleared: !redirectState.redirectUrl,
    onboardingCleared:
      onboardingState.currentStep === 0 &&
      !onboardingState.selectedRole &&
      !onboardingState.selectedAddress,
    chatsCleared: chatsState.chats.length === 0,
    widgetsCleared: widgetsState.properties.length === 0 && widgetsState.addresses.length === 0,
    localStorageCleared:
      !localStorage.getItem('user-test-data') &&
      !localStorage.getItem('chat-history') &&
      !localStorage.getItem('auth-storage') &&
      !localStorage.getItem('redirect-query-url-storage'),
    sessionStorageCleared: sessionStorage.length === 0,
    cookiesCleared: !hasAuthCookies(),
  };

  console.log('📊 Clear results:', results);

  const allCleared = Object.values(results).every(Boolean);
  console.log(allCleared ? '✅ All data cleared successfully!' : '❌ Some data was not cleared');

  return { results, allCleared };
};

/**
 * Run a complete test of the clear functionality
 */
export const testClearUserData = async () => {
  console.log('🧪 Starting clearUserData test...');

  // Add test data
  addTestData();

  // Wait a bit to ensure data is set
  await new Promise((resolve) => setTimeout(resolve, 100));

  // Clear all data
  await clearAllUserData();

  // Wait a bit to ensure clearing is complete
  await new Promise((resolve) => setTimeout(resolve, 200));

  // Check results
  const { allCleared } = checkDataCleared();

  return allCleared;
};

// Make functions available globally for testing in browser console
if (typeof window !== 'undefined') {
  (window as any).testClearUserData = testClearUserData;
  (window as any).addTestData = addTestData;
  (window as any).checkDataCleared = checkDataCleared;
}
