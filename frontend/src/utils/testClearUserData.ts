/**
 * Test utility to verify that clearAllUserData works correctly
 */

import { useAuthStore } from '@/stores/auth.store';
import { useRedirectQueryUrl } from '@/hooks/useRedirectQueryUrl';
import { useOnboardingStore } from '@/hooks/useOnboarding';
import { useChats } from '@/hooks/useChats';
import { useWidgets } from '@/hooks/useWidgets';
import { clearAllUserData } from './clearUserData';
import { UserPropertyRelationType } from '@/api/properties';
import { setCookie, hasAuthCookies, getAllCookies, aggressiveClearAuthCookies } from './cookies';

/**
 * Add some test data to stores and localStorage
 */
export const addTestData = () => {
  console.log('🧪 Adding test data...');

  // Add test data to stores
  useAuthStore.getState().setGuestAuth({
    token: 'test-token-123',
    userId: 123,
    expiresAt: new Date(Date.now() + 3600000).toISOString(),
  });

  useRedirectQueryUrl.getState().setRedirectUrl('https://example.com/test');

  useOnboardingStore.getState().setSelectedRole(UserPropertyRelationType.OwnerAndOccupier);
  useOnboardingStore.getState().setSelectedAddress('Test Address 123');

  useChats.setState({
    chats: [
      {
        id: 1,
        title: 'Test Chat',
        createdAt: new Date().toISOString(),
        status: '',
      },
    ],
  });

  // Add test localStorage data
  localStorage.setItem('user-test-data', 'test-value');
  localStorage.setItem('chat-history', 'test-chat-data');

  // Add test cookies
  setCookie('__clerk_db_jwt', 'test-jwt-token', { path: '/' });
  setCookie('__session', 'test-session-token', { path: '/' });
  setCookie('auth_token', 'test-auth-token', { path: '/' });

  console.log('✅ Test data added');
};

/**
 * Check if data was cleared properly
 */
export const checkDataCleared = () => {
  console.log('🔍 Checking if data was cleared...');

  const authState = useAuthStore.getState();
  const redirectState = useRedirectQueryUrl.getState();
  const onboardingState = useOnboardingStore.getState();
  const chatsState = useChats.getState();
  const widgetsState = useWidgets.getState();

  const results = {
    authCleared: !authState.guestAuth,
    redirectCleared: !redirectState.redirectUrl,
    onboardingCleared:
      onboardingState.currentStep === 0 &&
      !onboardingState.selectedRole &&
      !onboardingState.selectedAddress,
    chatsCleared: chatsState.chats.length === 0,
    widgetsCleared: widgetsState.properties.length === 0 && widgetsState.addresses.length === 0,
    localStorageCleared:
      !localStorage.getItem('user-test-data') &&
      !localStorage.getItem('chat-history') &&
      !localStorage.getItem('auth-storage') &&
      !localStorage.getItem('redirect-query-url-storage'),
    sessionStorageCleared: sessionStorage.length === 0,
    cookiesCleared: !hasAuthCookies(),
  };

  console.log('📊 Clear results:', results);

  const allCleared = Object.values(results).every(Boolean);
  console.log(allCleared ? '✅ All data cleared successfully!' : '❌ Some data was not cleared');

  return { results, allCleared };
};

/**
 * Run a complete test of the clear functionality
 */
export const testClearUserData = async () => {
  console.log('🧪 Starting clearUserData test...');

  // Add test data
  addTestData();

  // Wait a bit to ensure data is set
  await new Promise((resolve) => setTimeout(resolve, 100));

  // Clear all data
  await clearAllUserData();

  // Wait a bit to ensure clearing is complete
  await new Promise((resolve) => setTimeout(resolve, 200));

  // Check results
  const { allCleared } = checkDataCleared();

  return allCleared;
};

/**
 * Test aggressive cookie clearing specifically
 */
export const testAggressiveCookieClearing = async () => {
  console.log('🔥 Testing aggressive cookie clearing...');

  // Add test cookies including ones that mimic Clerk's pattern
  setCookie('__clerk_db_jwt_FLMvHKIX', 'test-jwt-token', { path: '/' });
  setCookie('__client_uat_FLMvHKIX', 'test-uat-token', { path: '/' });
  setCookie('__refresh_FLMvHKIX', 'test-refresh-token', { path: '/' });
  setCookie('clerk_active_context', 'test-context', { path: '/' });

  console.log('📊 Cookies before clearing:', getAllCookies());
  console.log('🔍 Has auth cookies before:', hasAuthCookies());

  // Try aggressive clearing
  await aggressiveClearAuthCookies();

  // Wait a bit
  await new Promise((resolve) => setTimeout(resolve, 200));

  console.log('📊 Cookies after clearing:', getAllCookies());
  console.log('🔍 Has auth cookies after:', hasAuthCookies());

  const success = !hasAuthCookies();
  console.log(
    success ? '✅ Aggressive cookie clearing succeeded!' : '❌ Some cookies still remain'
  );

  return success;
};

// Make functions available globally for testing in browser console
if (typeof window !== 'undefined') {
  (window as any).testClearUserData = testClearUserData;
  (window as any).addTestData = addTestData;
  (window as any).checkDataCleared = checkDataCleared;
  (window as any).testAggressiveCookieClearing = testAggressiveCookieClearing;
}
