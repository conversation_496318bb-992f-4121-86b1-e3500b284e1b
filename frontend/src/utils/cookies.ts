/**
 * Cookie utilities for managing browser cookies
 */

/**
 * Get a cookie value by name
 */
export const getCookie = (name: string): string | null => {
  if (typeof document === 'undefined') return null;
  
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
};

/**
 * Set a cookie
 */
export const setCookie = (
  name: string,
  value: string,
  options: {
    expires?: Date;
    maxAge?: number;
    path?: string;
    domain?: string;
    secure?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
  } = {}
): void => {
  if (typeof document === 'undefined') return;

  let cookieString = `${name}=${value}`;

  if (options.expires) {
    cookieString += `; expires=${options.expires.toUTCString()}`;
  }

  if (options.maxAge) {
    cookieString += `; max-age=${options.maxAge}`;
  }

  if (options.path) {
    cookieString += `; path=${options.path}`;
  }

  if (options.domain) {
    cookieString += `; domain=${options.domain}`;
  }

  if (options.secure) {
    cookieString += '; secure';
  }

  if (options.sameSite) {
    cookieString += `; samesite=${options.sameSite}`;
  }

  document.cookie = cookieString;
};

/**
 * Delete a cookie by setting it to expire in the past
 */
export const deleteCookie = (
  name: string,
  options: {
    path?: string;
    domain?: string;
  } = {}
): void => {
  if (typeof document === 'undefined') return;

  const expiredDate = new Date(0); // January 1, 1970
  
  // Try multiple combinations to ensure the cookie is deleted
  const pathOptions = [options.path || '/', '/'];
  const domainOptions = [
    options.domain,
    window.location.hostname,
    `.${window.location.hostname}`,
    undefined
  ].filter(Boolean);

  pathOptions.forEach(path => {
    domainOptions.forEach(domain => {
      // Delete without secure flag
      setCookie(name, '', {
        expires: expiredDate,
        path,
        domain,
      });
      
      // Delete with secure flag
      setCookie(name, '', {
        expires: expiredDate,
        path,
        domain,
        secure: true,
      });
    });
  });
};

/**
 * Clear all cookies that match a pattern
 */
export const clearCookiesByPattern = (pattern: RegExp | string): void => {
  if (typeof document === 'undefined') return;

  const allCookies = document.cookie.split(';');
  const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;

  allCookies.forEach((cookie) => {
    const cookieName = cookie.split('=')[0].trim();
    if (regex.test(cookieName)) {
      deleteCookie(cookieName);
    }
  });
};

/**
 * Clear authentication-related cookies
 */
export const clearAuthCookies = (): void => {
  console.log('🍪 Clearing authentication cookies...');

  // List of known authentication cookies
  const authCookieNames = [
    '__clerk_db_jwt',
    '__session',
    '__clerk_client_jwt',
    '__clerk_session',
    '__clerk_refresh_token',
    '__clerk_handshake',
    '__clerk_ssr_jwt',
    '__clerk_client_uat',
    '__clerk_db_jwt_refresh',
    '__clerk_db_jwt_session',
    '__clerk_db_jwt_client',
    '__clerk_db_jwt_handshake',
    '__clerk_db_jwt_ssr',
    '__clerk_db_jwt_uat',
    'auth_token',
    'session_token',
    'user_session',
  ];

  // Clear specific cookies
  authCookieNames.forEach(cookieName => {
    deleteCookie(cookieName);
  });

  // Clear any cookies that start with __clerk or __session
  clearCookiesByPattern(/^(__clerk|__session)/);
  
  console.log('✅ Authentication cookies cleared');
};

/**
 * Get all cookies as an object
 */
export const getAllCookies = (): Record<string, string> => {
  if (typeof document === 'undefined') return {};

  const cookies: Record<string, string> = {};
  document.cookie.split(';').forEach((cookie) => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookies[name] = decodeURIComponent(value);
    }
  });
  return cookies;
};

/**
 * Check if any authentication cookies exist
 */
export const hasAuthCookies = (): boolean => {
  const authCookiePatterns = [
    /^__clerk/,
    /^__session/,
    /^auth_token$/,
    /^session_token$/,
    /^user_session$/,
  ];

  const allCookies = getAllCookies();
  const cookieNames = Object.keys(allCookies);

  return cookieNames.some(name => 
    authCookiePatterns.some(pattern => pattern.test(name))
  );
};
