export const getCookie = (name: string): string | null => {
  if (typeof document === 'undefined') return null;
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
};

export const deleteCookie = (name: string): void => {
  if (typeof document === 'undefined') return;
  
  const expiredDate = new Date(0);
  const hostname = window.location.hostname;
  const domains = [hostname, `.${hostname}`, '.heyalfie.com', '.clerk.com'];
  const paths = ['/', ''];

  paths.forEach(path => {
    domains.forEach(domain => {
      try {
        document.cookie = `${name}=; expires=${expiredDate.toUTCString()}; path=${path}; domain=${domain}`;
        document.cookie = `${name}=; expires=${expiredDate.toUTCString()}; path=${path}; domain=${domain}; secure`;
      } catch (error) {}
    });
  });
};

export const getAllCookies = (): Record<string, string> => {
  if (typeof document === 'undefined') return {};
  const cookies: Record<string, string> = {};
  document.cookie.split(';').forEach((cookie) => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookies[name] = decodeURIComponent(value);
    }
  });
  return cookies;
};

export const aggressiveClearAuthCookies = async (): Promise<void> => {
  const allCookies = getAllCookies();
  const cookieNames = Object.keys(allCookies);
  
  const authCookiePatterns = [
    /^__clerk/,
    /^__session/,
    /^__client_uat/,
    /^__refresh/,
    /^auth_token$/,
    /^session_token$/,
    /^user_session$/,
    /^clerk_active_context$/,
  ];

  const cookiesToDelete = cookieNames.filter((name) =>
    authCookiePatterns.some((pattern) => pattern.test(name))
  );

  cookiesToDelete.forEach((cookieName) => {
    deleteCookie(cookieName);
  });

  const knownAuthCookies = [
    '__clerk_db_jwt',
    '__session',
    '__clerk_client_jwt',
    '__clerk_session',
    '__clerk_refresh_token',
    '__clerk_handshake',
    '__clerk_ssr_jwt',
    '__client_uat',
    'auth_token',
    'session_token',
    'user_session',
    'clerk_active_context',
  ];

  knownAuthCookies.forEach((cookieName) => {
    deleteCookie(cookieName);
  });

  const clerkSuffixes = ['_FLMvHKIX', '_DvIHvy3E', '_-Ti8aGhv'];
  const clerkPrefixes = ['__clerk_db_jwt', '__session', '__client_uat', '__refresh'];

  clerkPrefixes.forEach((prefix) => {
    clerkSuffixes.forEach((suffix) => {
      deleteCookie(`${prefix}${suffix}`);
    });
  });

  try {
    fetch('/api/auth/clear-cookies', {
      method: 'POST',
      credentials: 'include',
    }).catch(() => {});
  } catch (error) {}
};
