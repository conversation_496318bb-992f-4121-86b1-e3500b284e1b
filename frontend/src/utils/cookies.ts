export const getCookie = (name: string): string | null => {
export const getCookie = (name: string): string | null => {
  if (typeof document === 'undefined') return null;

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
};

/**
 * Set a cookie
 */
export const setCookie = (
  name: string,
  value: string,
  options: {
    expires?: Date;
    maxAge?: number;
    path?: string;
    domain?: string;
    secure?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
  } = {}
): void => {
  if (typeof document === 'undefined') return;

  let cookieString = `${name}=${value}`;

  if (options.expires) {
    cookieString += `; expires=${options.expires.toUTCString()}`;
  }

  if (options.maxAge) {
    cookieString += `; max-age=${options.maxAge}`;
  }

  if (options.path) {
    cookieString += `; path=${options.path}`;
  }

  if (options.domain) {
    cookieString += `; domain=${options.domain}`;
  }

  if (options.secure) {
    cookieString += '; secure';
  }

  if (options.sameSite) {
    cookieString += `; samesite=${options.sameSite}`;
  }

  document.cookie = cookieString;
};

/**
 * Delete a cookie by setting it to expire in the past
 * This function tries multiple combinations to ensure the cookie is deleted
 * regardless of how it was originally set
 */
export const deleteCookie = (
  name: string,
  options: {
    path?: string;
    domain?: string;
  } = {}
): void => {
  if (typeof document === 'undefined') return;

  const expiredDate = new Date(0); // January 1, 1970

  // Get current hostname for domain variations
  const hostname = window.location.hostname;

  // Try multiple path combinations
  const pathOptions = [options.path || '/', '/', ''];

  // Try multiple domain combinations
  const domainOptions = [
    options.domain,
    hostname,
    `.${hostname}`,
    '.heyalfie.com',
    '.clerk.com',
    '.summary-boa-78.clerk.accounts.dev',
    undefined, // No domain specified
  ].filter((domain, index, arr) => domain !== undefined && arr.indexOf(domain) === index); // Remove duplicates and undefined

  // Try all combinations of path, domain, and security flags
  pathOptions.forEach((path) => {
    domainOptions.forEach((domain) => {
      // Try different combinations of flags
      const flagCombinations = [
        {}, // No flags
        { secure: true }, // Secure only
        { sameSite: 'lax' as const }, // SameSite only
        { secure: true, sameSite: 'lax' as const }, // Both
        { sameSite: 'strict' as const }, // Strict
        { secure: true, sameSite: 'strict' as const }, // Secure + Strict
        { sameSite: 'none' as const }, // None
        { secure: true, sameSite: 'none' as const }, // Secure + None (required for SameSite=None)
      ];

      flagCombinations.forEach((flags) => {
        setCookie(name, '', {
          expires: expiredDate,
          path,
          domain,
          ...flags,
        });
      });
    });
  });

  // Also try direct document.cookie manipulation for stubborn cookies
  const cookieStrings = [
    `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`,
    `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${hostname};`,
    `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${hostname};`,
    `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure;`,
    `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${hostname}; secure;`,
    `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${hostname}; secure;`,
    `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.heyalfie.com;`,
    `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.clerk.com;`,
    `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.summary-boa-78.clerk.accounts.dev;`,
  ];

  cookieStrings.forEach((cookieString) => {
    try {
      document.cookie = cookieString;
    } catch (error) {
      // Ignore errors for invalid combinations
    }
  });
};

/**
 * Clear all cookies that match a pattern
 */
export const clearCookiesByPattern = (pattern: RegExp | string): void => {
  if (typeof document === 'undefined') return;

  const allCookies = document.cookie.split(';');
  const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;

  allCookies.forEach((cookie) => {
    const cookieName = cookie.split('=')[0].trim();
    if (regex.test(cookieName)) {
      deleteCookie(cookieName);
    }
  });
};

/**
 * Clear authentication-related cookies
 */
export const clearAuthCookies = (): void => {
  console.log('🍪 Clearing authentication cookies...');

  // First, get all existing cookies to find dynamic ones
  const allCookies = getAllCookies();
  const cookieNames = Object.keys(allCookies);

  // Find all cookies that match authentication patterns
  const authCookiePatterns = [
    /^__clerk/, // All Clerk cookies
    /^__session/, // Session cookies
    /^__client_uat/, // Client UAT cookies
    /^__refresh/, // Refresh tokens
    /^auth_token$/, // Auth tokens
    /^session_token$/, // Session tokens
    /^user_session$/, // User session
    /^clerk_active_context$/, // Clerk context
  ];

  // Find all cookies that match any of the patterns
  const cookiesToDelete = cookieNames.filter((name) =>
    authCookiePatterns.some((pattern) => pattern.test(name))
  );

  console.log('🔍 Found authentication cookies to delete:', cookiesToDelete);

  // Delete each found cookie
  cookiesToDelete.forEach((cookieName) => {
    console.log(`🗑️ Deleting cookie: ${cookieName}`);
    deleteCookie(cookieName);
  });

  // Also try to clear some known static cookie names that might not be in document.cookie
  // (some cookies might be HttpOnly and not accessible via JavaScript)
  const knownAuthCookies = [
    '__clerk_db_jwt',
    '__session',
    '__clerk_client_jwt',
    '__clerk_session',
    '__clerk_refresh_token',
    '__clerk_handshake',
    '__clerk_ssr_jwt',
    '__client_uat',
    'auth_token',
    'session_token',
    'user_session',
    'clerk_active_context',
  ];

  knownAuthCookies.forEach((cookieName) => {
    deleteCookie(cookieName);
  });

  // Try to clear cookies with common Clerk suffixes
  const clerkSuffixes = ['_FLMvHKIX', '_DvIHvy3E', '_-Ti8aGhv']; // Add more as needed
  const clerkPrefixes = ['__clerk_db_jwt', '__session', '__client_uat', '__refresh'];

  clerkPrefixes.forEach((prefix) => {
    clerkSuffixes.forEach((suffix) => {
      const cookieName = `${prefix}${suffix}`;
      deleteCookie(cookieName);
    });
  });

  // For HttpOnly cookies that can't be deleted via JavaScript,
  // we need to make a request to the server to clear them
  // This is a fallback approach
  try {
    // Try to make a request to clear server-side cookies
    fetch('/api/auth/clear-cookies', {
      method: 'POST',
      credentials: 'include', // Include cookies in the request
    }).catch(() => {
      // Ignore errors - this endpoint might not exist
      console.log('ℹ️ Server-side cookie clearing endpoint not available');
    });
  } catch (error) {
    // Ignore errors
  }

  console.log('✅ Authentication cookies clearing attempted');
};

/**
 * Aggressive cookie clearing function that tries multiple approaches
 * This function should be used when standard cookie clearing doesn't work
 */
export const aggressiveClearAuthCookies = async (): Promise<void> => {
  console.log('🔥 Starting aggressive authentication cookie clearing...');

  // Step 1: Standard clearing
  clearAuthCookies();

  // Step 2: Wait a bit and try again
  await new Promise((resolve) => setTimeout(resolve, 100));

  // Step 3: Try to clear all cookies we can see
  const allCookies = getAllCookies();
  Object.keys(allCookies).forEach((cookieName) => {
    if (
      cookieName.includes('clerk') ||
      cookieName.includes('session') ||
      cookieName.includes('auth') ||
      cookieName.includes('refresh') ||
      cookieName.includes('client_uat')
    ) {
      console.log(`🔥 Aggressively deleting: ${cookieName}`);

      // Try multiple deletion approaches
      deleteCookie(cookieName);

      // Try direct manipulation with different formats
      const formats = [
        `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; max-age=0;`,
        `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname}; max-age=0;`,
        `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname}; max-age=0;`,
        `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.heyalfie.com; max-age=0;`,
        `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.clerk.com; max-age=0;`,
        `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; max-age=0;`,
        `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; samesite=none; secure; max-age=0;`,
        `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; samesite=lax; max-age=0;`,
        `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; samesite=strict; max-age=0;`,
      ];

      formats.forEach((format) => {
        try {
          document.cookie = format;
        } catch (error) {
          // Ignore errors
        }
      });
    }
  });

  console.log('🔥 Aggressive cookie clearing completed');
};

/**
 * Nuclear option: Force clear cookies by reloading the page
 * This should only be used as a last resort
 */
export const forceClearCookiesWithReload = (): void => {
  console.log('💥 Nuclear cookie clearing: forcing page reload...');

  // Clear what we can first
  aggressiveClearAuthCookies().then(() => {
    // Force a hard reload to clear any remaining cookies
    window.location.href = window.location.href;
  });
};

/**
 * Get all cookies as an object
 */
export const getAllCookies = (): Record<string, string> => {
  if (typeof document === 'undefined') return {};

  const cookies: Record<string, string> = {};
  document.cookie.split(';').forEach((cookie) => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookies[name] = decodeURIComponent(value);
    }
  });
  return cookies;
};

/**
 * Check if any authentication cookies exist
 */
export const hasAuthCookies = (): boolean => {
  const authCookiePatterns = [
    /^__clerk/,
    /^__session/,
    /^auth_token$/,
    /^session_token$/,
    /^user_session$/,
  ];

  const allCookies = getAllCookies();
  const cookieNames = Object.keys(allCookies);

  return cookieNames.some((name) => authCookiePatterns.some((pattern) => pattern.test(name)));
};
