/**
 * Minimal utility to clear all user data on sign out
 */

import { mutate } from 'swr';
import { useAuthStore } from '@/stores/auth.store';
import { useRedirectQueryUrl } from '@/hooks/useRedirectQueryUrl';
import { useChats } from '@/hooks/useChats';
import { useWidgets } from '@/hooks/useWidgets';
import { useMessages } from '@/hooks/useMessages';
import { useAppliances } from '@/hooks/useAppliances';
import { useOnboardingStore } from '@/hooks/useOnboarding';
import { useStreamingState } from '@/hooks/useStreamingState';
import { useConversionState } from '@/hooks/useGuestConversion';
import { useSidebar } from '@/hooks/useSidebar';
import { useModalForGuest } from '@/hooks/useModalGuest';
import { createGuestUser } from '@/api/user';

/**
 * Clear all user data when signing out
 */
export const clearAllUserData = async (): Promise<void> => {
  console.log('🧹 Clearing all user data...');

  try {
    // 1. Clear SWR cache
    console.log('🔄 Clearing SWR cache...');
    mutate(() => true, undefined, { revalidate: false });

    // 2. Clear Zustand stores with persist (localStorage)
    console.log('🔄 Clearing persisted stores...');
    useAuthStore.getState().logout(); // This clears auth-storage
    useRedirectQueryUrl.getState().clearRedirectUrl(); // This clears redirect-query-url-storage

    // Force clear persist storage keys
    localStorage.removeItem('auth-storage');
    localStorage.removeItem('redirect-query-url-storage');

    // 3. Reset in-memory Zustand stores
    console.log('🔄 Resetting in-memory stores...');
    useChats.setState({
      chats: [],
      isLoading: false,
      isSubmitting: false,
      hasMore: true,
      currentPage: 1,
      optimisticMessage: null,
      jobSummaryConfirmed: false,
      isRetryButtonShown: false,
      isMessagesSpacingActive: false,
      isStreamingMessage: false,
    });

    useWidgets.setState({
      addresses: [],
      properties: [],
      hasAddress: true,
      addressData: null,
      accessInstruction: '',
      propertiesResponse: null,
      addressValue: null,
      isLoadingProperties: false,
      isLoadingAddresses: false,
      isSavingAddress: false,
    });

    useMessages.setState({
      isLoading: false,
      hasMore: true,
      currentPage: 1,
      jobSummaryConfirmed: false,
      messageHeight: { optimistic: 0, lastSystem: 0 },
    });

    // Reset appliances store if it has a reset method
    const appliancesState = useAppliances.getState();
    if ('reset' in appliancesState && typeof appliancesState.reset === 'function') {
      appliancesState.reset();
    } else {
      useAppliances.setState({
        appliances: [],
        editingAppliances: [],
        currentPage: 1,
        totalPages: null,
        hasMoreItems: true,
        isLoadingMore: false,
        isLoading: false,
        isCreating: false,
        isUpdating: false,
        isFetching: false,
      });
    }

    // Reset onboarding store
    useOnboardingStore.getState().reset();

    useStreamingState.setState({
      isStreamingMessage: false,
    });

    useConversionState.setState({
      isConverting: false,
    });

    useSidebar.setState({
      isSidebarOpen: false,
    });

    useModalForGuest.setState({
      isModalOpen: false,
    });

    // 4. Clear any additional localStorage keys that might contain user data
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (
        key &&
        (key.startsWith('user-') ||
          key.startsWith('guest-') ||
          key.startsWith('chat-') ||
          key.startsWith('property-') ||
          key.startsWith('todo-') ||
          key.startsWith('alfie-'))
      ) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach((key) => localStorage.removeItem(key));

    // 5. Clear sessionStorage
    sessionStorage.clear();

    // 6. Create new guest user for clean start
    try {
      const response = await createGuestUser();
      useAuthStore.getState().setGuestAuth({
        token: response.token,
        userId: response.userId,
        expiresAt: response.expiresAt,
      });
      console.log('✅ New guest user created');
    } catch (error) {
      console.error('❌ Failed to create new guest user:', error);
    }

    console.log('✅ User data cleared successfully');

    // Force re-initialization of persist stores to ensure they pick up cleared localStorage
    useAuthStore.persist.rehydrate();
    useRedirectQueryUrl.persist.rehydrate();

    // Force a small delay to ensure all state updates are processed
    await new Promise((resolve) => setTimeout(resolve, 100));
  } catch (error) {
    console.error('❌ Error clearing user data:', error);
    throw error;
  }
};
