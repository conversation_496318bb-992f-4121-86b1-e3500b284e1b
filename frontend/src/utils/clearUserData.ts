import { mutate } from 'swr';
import { useAuthStore } from '@/stores/auth.store';
import { useRedirectQueryUrl } from '@/hooks/useRedirectQueryUrl';
import { useOnboarding } from '@/hooks/useOnboarding';
import { useChats } from '@/hooks/useChats';
import { useWidgets } from '@/hooks/useWidgets';
import { useMessages } from '@/hooks/useMessages';
import { useAppliances } from '@/hooks/useAppliances';
import { useStreamingState } from '@/hooks/useStreamingState';

import { useSidebar } from '@/hooks/useSidebar';
import { useModalForGuest } from '@/hooks/useModalGuest';
import { createGuestUser } from '@/api/user';
import { aggressiveClearAuthCookies } from './cookies';

export const clearAllUserData = async (): Promise<void> => {
  try {
    // Clear all SWR cache
    mutate(() => true, undefined, { revalidate: false });

    // Clear specific chat-related SWR cache keys
    mutate((key) => typeof key === 'string' && key.includes('/chats'), undefined, {
      revalidate: false,
    });
    mutate((key) => typeof key === 'string' && key.includes('/messages'), undefined, {
      revalidate: false,
    });
    mutate((key) => typeof key === 'string' && key.includes('/todos'), undefined, {
      revalidate: false,
    });

    useAuthStore.getState().logout();
    useRedirectQueryUrl.getState().clearRedirectUrl();
    localStorage.removeItem('auth-storage');
    localStorage.removeItem('redirect-query-url-storage');

    useChats.setState({
      chats: [],
      isLoading: false,
      isSubmitting: false,
      hasMore: true,
      currentPage: 1,
      optimisticMessage: null,
      jobSummaryConfirmed: false,
      isRetryButtonShown: false,
      isMessagesSpacingActive: false,
      isStreamingMessage: false,
    });

    useWidgets.setState({
      addresses: [],
      properties: [],
      hasAddress: true,
      addressData: null,
      accessInstruction: '',
      propertiesResponse: null,
      addressValue: null,
      isLoadingProperties: false,
      isLoadingAddresses: false,
      isSavingAddress: false,
    });

    useMessages.setState({
      isLoading: false,
      hasMore: true,
      currentPage: 1,
      jobSummaryConfirmed: false,
      messageHeight: { optimistic: 0, lastSystem: 0 },
    });

    const appliancesState = useAppliances.getState();
    if ('reset' in appliancesState && typeof appliancesState.reset === 'function') {
      appliancesState.reset();
    } else {
      useAppliances.setState({
        appliances: [],
        editingAppliances: [],
        currentPage: 1,
        totalPages: null,
        hasMoreItems: true,
        isLoadingMore: false,
        isLoading: false,
        isCreating: false,
        isUpdating: false,
        isFetching: false,
      });
    }

    useOnboarding().reset();
    useStreamingState.setState({ isStreamingMessage: false });
    useSidebar.setState({ isSidebarOpen: false });
    useModalForGuest.setState({ isModalOpen: false });

    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (
        key &&
        (key.startsWith('user-') ||
          key.startsWith('guest-') ||
          key.startsWith('chat-') ||
          key.startsWith('property-') ||
          key.startsWith('todo-') ||
          key.startsWith('alfie-') ||
          key.startsWith('swr-') ||
          key.includes('chats') ||
          key.includes('messages') ||
          key.includes('recent'))
      ) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach((key) => localStorage.removeItem(key));

    sessionStorage.clear();
    await aggressiveClearAuthCookies();

    try {
      const response = await createGuestUser();
      useAuthStore.getState().setGuestAuth({
        token: response.token,
        userId: response.userId,
        expiresAt: response.expiresAt,
      });
    } catch (error) {}

    useAuthStore.persist.rehydrate();
    useRedirectQueryUrl.persist.rehydrate();
    await new Promise((resolve) => setTimeout(resolve, 100));
  } catch (error) {
    throw error;
  }
};

export const completeLogout = async (clerkSignOut?: () => Promise<void>): Promise<void> => {
  try {
    if (clerkSignOut) {
      await clerkSignOut();
      await new Promise((resolve) => setTimeout(resolve, 500));
    }
    await clearAllUserData();
    await aggressiveClearAuthCookies();
    await new Promise((resolve) => setTimeout(resolve, 300));
  } catch (error) {
    throw error;
  }
};
