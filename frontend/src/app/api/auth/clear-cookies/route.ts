import { NextRequest, NextResponse } from 'next/server';

/**
 * API endpoint to clear authentication cookies from the server side
 * This is necessary for HttpOnly cookies that can't be cleared via JavaScript
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🍪 Server-side cookie clearing requested');

    // List of authentication cookies to clear
    const authCookieNames = [
      '__clerk_db_jwt',
      '__session',
      '__clerk_client_jwt',
      '__clerk_session',
      '__clerk_refresh_token',
      '__clerk_handshake',
      '__clerk_ssr_jwt',
      '__client_uat',
      '__refresh',
      'auth_token',
      'session_token',
      'user_session',
      'clerk_active_context',
    ];

    // Also look for dynamic Clerk cookies with suffixes
    const clerkSuffixes = ['_FLMvHKIX', '_DvIHvy3E', '_-Ti8aGhv'];
    const clerkPrefixes = ['__clerk_db_jwt', '__session', '__client_uat', '__refresh'];
    
    const dynamicCookieNames: string[] = [];
    clerkPrefixes.forEach(prefix => {
      clerkSuffixes.forEach(suffix => {
        dynamicCookieNames.push(`${prefix}${suffix}`);
      });
    });

    const allCookiesToClear = [...authCookieNames, ...dynamicCookieNames];

    // Create response with Set-Cookie headers to clear cookies
    const response = NextResponse.json({ 
      success: true, 
      message: 'Cookies cleared',
      clearedCookies: allCookiesToClear 
    });

    // Set cookies to expire in the past for different domain/path combinations
    const hostname = request.nextUrl.hostname;
    const domains = [
      hostname,
      `.${hostname}`,
      '.heyalfie.com',
      '.clerk.com',
      '.summary-boa-78.clerk.accounts.dev',
    ];

    const paths = ['/', ''];

    allCookiesToClear.forEach(cookieName => {
      domains.forEach(domain => {
        paths.forEach(path => {
          // Clear with different combinations
          const cookieOptions = [
            `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain}; max-age=0`,
            `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain}; secure; max-age=0`,
            `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain}; httponly; max-age=0`,
            `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain}; secure; httponly; max-age=0`,
            `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain}; samesite=lax; max-age=0`,
            `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain}; samesite=strict; max-age=0`,
            `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain}; samesite=none; secure; max-age=0`,
          ];

          cookieOptions.forEach(cookieString => {
            response.headers.append('Set-Cookie', cookieString);
          });
        });
      });

      // Also try without domain specification
      response.headers.append('Set-Cookie', `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; max-age=0`);
      response.headers.append('Set-Cookie', `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; max-age=0`);
      response.headers.append('Set-Cookie', `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; httponly; max-age=0`);
      response.headers.append('Set-Cookie', `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; httponly; max-age=0`);
    });

    console.log('✅ Server-side cookie clearing headers set');
    return response;

  } catch (error) {
    console.error('❌ Error clearing cookies on server:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to clear cookies' },
      { status: 500 }
    );
  }
}

// Also handle GET requests for testing
export async function GET() {
  return NextResponse.json({ 
    message: 'Cookie clearing endpoint is available. Use POST to clear cookies.' 
  });
}
