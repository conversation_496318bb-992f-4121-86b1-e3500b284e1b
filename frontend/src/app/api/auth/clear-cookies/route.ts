import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const authCookieNames = [
      '__clerk_db_jwt',
      '__session',
      '__clerk_client_jwt',
      '__clerk_session',
      '__clerk_refresh_token',
      '__clerk_handshake',
      '__clerk_ssr_jwt',
      '__client_uat',
      '__refresh',
      'auth_token',
      'session_token',
      'user_session',
      'clerk_active_context',
    ];

    const clerkSuffixes = ['_FLMvHKIX', '_DvIHvy3E', '_-Ti8aGhv'];
    const clerkPrefixes = ['__clerk_db_jwt', '__session', '__client_uat', '__refresh'];
    const dynamicCookieNames: string[] = [];
    clerkPrefixes.forEach((prefix) => {
      clerkSuffixes.forEach((suffix) => {
        dynamicCookieNames.push(`${prefix}${suffix}`);
      });
    });

    const allCookiesToClear = [...authCookieNames, ...dynamicCookieNames];
    const response = NextResponse.json({ success: true });
    const hostname = request.nextUrl.hostname;
    const domains = [
      hostname,
      `.${hostname}`,
      '.heyalfie.com',
      '.clerk.com',
      '.summary-boa-78.clerk.accounts.dev',
    ];
    const paths = ['/', ''];

    allCookiesToClear.forEach((cookieName) => {
      domains.forEach((domain) => {
        paths.forEach((path) => {
          const cookieOptions = [
            `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain}; max-age=0`,
            `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain}; secure; max-age=0`,
            `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain}; httponly; max-age=0`,
          ];
          cookieOptions.forEach((cookieString) => {
            response.headers.append('Set-Cookie', cookieString);
          });
        });
      });
      response.headers.append(
        'Set-Cookie',
        `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; max-age=0`
      );
    });

    return response;
  } catch (error) {
    return NextResponse.json({ success: false }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ message: 'Cookie clearing endpoint available' });
}
