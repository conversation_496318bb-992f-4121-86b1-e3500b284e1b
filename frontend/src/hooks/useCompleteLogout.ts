import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';
import { completeLogout } from '@/utils/clearUserData';

/**
 * Hook that provides a complete logout function
 * This integrates Clerk's signOut with our comprehensive data clearing
 */
export const useCompleteLogout = () => {
  const { signOut } = useAuth();
  const router = useRouter();

  const logout = useCallback(async (options?: { 
    redirectTo?: string;
    forceReload?: boolean;
  }) => {
    try {
      console.log('🚪 Starting complete logout...');

      // Use our complete logout function with Clerk's signOut
      await completeLogout(async () => {
        await signOut();
      });

      // Navigate to the specified page or home
      const redirectTo = options?.redirectTo || '/';
      
      if (options?.forceReload) {
        // Force a complete page reload to ensure all state is cleared
        window.location.href = window.location.origin + redirectTo;
      } else {
        router.push(redirectTo);
      }

      console.log('✅ Complete logout successful');

    } catch (error) {
      console.error('❌ Error during complete logout:', error);
      
      // Fallback: force reload to ensure logout
      console.log('🔄 Fallback: forcing page reload...');
      window.location.href = window.location.origin + (options?.redirectTo || '/');
    }
  }, [signOut, router]);

  return { logout };
};

/**
 * Hook for emergency logout that forces a page reload
 * Use this when normal logout doesn't work properly
 */
export const useEmergencyLogout = () => {
  const { signOut } = useAuth();

  const emergencyLogout = useCallback(async () => {
    try {
      console.log('🚨 Emergency logout initiated...');
      
      // Try Clerk signOut first
      await signOut();
      
      // Force immediate reload
      window.location.href = window.location.origin;
      
    } catch (error) {
      console.error('❌ Error during emergency logout:', error);
      
      // Force reload anyway
      window.location.href = window.location.origin;
    }
  }, [signOut]);

  return { emergencyLogout };
};
