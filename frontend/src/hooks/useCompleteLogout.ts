import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';
import { completeLogout } from '@/utils/clearUserData';

export const useCompleteLogout = () => {
  const { signOut } = useAuth();
  const router = useRouter();

  const logout = useCallback(
    async (options?: { redirectTo?: string; forceReload?: boolean }) => {
      try {
        await completeLogout(async () => {
          await signOut();
        });

        const redirectTo = options?.redirectTo || '/';

        if (options?.forceReload) {
          window.location.href = window.location.origin + redirectTo;
        } else {
          router.push(redirectTo);
        }
      } catch (error) {
        window.location.href = window.location.origin + (options?.redirectTo || '/');
      }
    },
    [signOut, router]
  );

  return { logout };
};
