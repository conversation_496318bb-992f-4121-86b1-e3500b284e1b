/**
 * Example of how to integrate the complete logout functionality
 * This shows how to modify the Header component to use the new logout system
 */

'use client';

import React, { useEffect, useRef } from 'react';
import { UserButton } from '@clerk/nextjs';
import { Logo } from '@/components/Sidebar/components/Logo/Logo';
import { IconSvgObject } from '@hugeicons/react';
import { Menu01Icon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { useSidebar } from '@/hooks/useSidebar';
import { Button } from '@/components/Button/Button';
import { ButtonType, ButtonColor, ButtonSize } from '@/components/Button/Button.types';
import { SignInButton } from '@clerk/nextjs';
import { useAuth } from '@clerk/nextjs';
import { SignUpButton } from '@clerk/nextjs';
import { useCompleteLogout } from '@/hooks/useCompleteLogout';
import { useRouter } from 'next/navigation';

import styles from './Header.module.scss';

export const HeaderWithCompleteLogout: React.FC = () => {
  const { isMobile } = useBreakpoint();
  const { toggleSidebar } = useSidebar();
  const { isSignedIn } = useAuth();
  const { logout } = useCompleteLogout();
  const router = useRouter();
  const wasSignedInRef = useRef(isSignedIn);

  // Detect sign out and clear data
  useEffect(() => {
    if (wasSignedInRef.current && !isSignedIn) {
      // User just signed out via UserButton or other means
      // The complete logout should have been handled already,
      // but we can add additional cleanup here if needed
      console.log('🔍 Sign out detected, ensuring complete cleanup...');
      
      // Optional: Add any additional cleanup logic here
      // The completeLogout function should have already been called
    }
    wasSignedInRef.current = isSignedIn;
  }, [isSignedIn]);

  // Custom logout handler for manual logout buttons
  const handleManualLogout = async () => {
    try {
      // Use the complete logout function
      await logout({ 
        redirectTo: '/',
        forceReload: false // Set to true if cookies are still problematic
      });
    } catch (error) {
      console.error('Manual logout failed:', error);
      // Fallback to emergency logout
      window.location.href = '/';
    }
  };

  return (
    <header className={styles.header}>
      <div className={styles.headerContent}>
        {/* Logo and menu button */}
        <div className={styles.leftSection}>
          {isMobile && (
            <Button
              type={ButtonType.ICON}
              color={ButtonColor.TRANSPARENT}
              size={ButtonSize.MEDIUM}
              onClick={toggleSidebar}
              className={styles.menuButton}
            >
              <HugeiconsIcon icon={Menu01Icon as IconSvgObject} />
            </Button>
          )}
          <Logo />
        </div>

        {/* Auth section */}
        <div className={styles.rightSection}>
          {isSignedIn ? (
            <div className={styles.userSection}>
              {/* Option 1: Use Clerk's UserButton (recommended) */}
              <UserButton 
                afterSignOutUrl="/"
                // You can customize the UserButton to trigger our complete logout
              />
              
              {/* Option 2: Custom logout button */}
              {/* 
              <Button
                type={ButtonType.SECONDARY}
                color={ButtonColor.GRAY}
                size={ButtonSize.SMALL}
                onClick={handleManualLogout}
              >
                Sign Out
              </Button>
              */}
            </div>
          ) : (
            <div className={styles.authButtons}>
              <SignInButton mode="modal">
                <Button
                  type={ButtonType.SECONDARY}
                  color={ButtonColor.GRAY}
                  size={ButtonSize.SMALL}
                >
                  Sign In
                </Button>
              </SignInButton>
              <SignUpButton mode="modal">
                <Button
                  type={ButtonType.PRIMARY}
                  color={ButtonColor.GREEN}
                  size={ButtonSize.SMALL}
                >
                  Sign Up
                </Button>
              </SignUpButton>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

/**
 * Alternative approach: Custom UserButton with complete logout
 */
export const CustomUserButtonWithCompleteLogout: React.FC = () => {
  const { logout } = useCompleteLogout();

  const handleSignOut = async () => {
    await logout({ 
      redirectTo: '/',
      forceReload: true // Force reload to ensure all cookies are cleared
    });
  };

  return (
    <UserButton>
      <UserButton.MenuItems>
        <UserButton.Action 
          label="Sign out" 
          labelIcon={<span>🚪</span>}
          onClick={handleSignOut}
        />
      </UserButton.MenuItems>
    </UserButton>
  );
};
