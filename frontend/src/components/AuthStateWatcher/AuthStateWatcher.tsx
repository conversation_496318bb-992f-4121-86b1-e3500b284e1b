'use client';

import { useAuth } from '@clerk/nextjs';
import { useEffect, useRef } from 'react';
import { clearAllUserData } from '@/utils/clearUserData';

export const AuthStateWatcher = () => {
  const { isSignedIn, isLoaded } = useAuth();
  const wasSignedInRef = useRef<boolean | null>(null);

  useEffect(() => {
    if (!isLoaded) return;

    // Initialize the ref on first load
    if (wasSignedInRef.current === null) {
      wasSignedInRef.current = isSignedIn;
      return;
    }

    // Detect sign out: was signed in, now not signed in
    if (wasSignedInRef.current && !isSignedIn) {
      clearAllUserData().catch((error) => {
        console.error('Failed to clear user data on sign out:', error);
      });
    }

    // Update the ref
    wasSignedInRef.current = isSignedIn;
  }, [isSignedIn, isLoaded]);

  return null; // This component doesn't render anything
};
